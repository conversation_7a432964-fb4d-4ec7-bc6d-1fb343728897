import { useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Snackbar, Alert } from '@mui/material';
import './App.css';

// Import providers
import AppStateProvider, { useAppState } from './providers/AppStateProvider';
import SessionStateProvider from './providers/SessionStateProvider';
import RecordingSessionProvider from './providers/RecordingSessionProvider';

// Import main application component
import AppContent from './components/AppContent';

/**
 * Complete Data Reset Service for Privacy Between Users
 * Clears all localStorage and sessionStorage data on page refresh
 * to ensure each user starts with a completely clean slate
 */
const performCompleteDataReset = () => {
  console.log('🔄 Performing complete data reset for user privacy...');

  try {
    // Define all known localStorage keys used by the ICU Dataset Application
    const icuAppKeys = [
      // Demographics and user data
      'icuAppDemographics',
      'icuAppConsent',

      // Session and recording state
      'icuAppRecordingsCount',
      'icuAppProgressData',
      'icuAppUserSession',
      'icuAppCurrentPhraseCount',
      'icuAppSessionActive',

      // Phrase selection and completion
      'icu_selected_phrases',
      'icuAppSelectedPhrases',
      'icuAppCompletedPhrases',
      'icu_completed_phrases',

      // Receipt system
      'icuAppReceiptCounter',
      'icuAppReceiptLog',

      // Reference numbers and mappings
      'icuAppReferenceNumbers',
      'icuAppCurrentSession',

      // Receipt mappings (individual receipt mappings use dynamic keys)
      // These will be caught by the pattern matching below

      // Testing and debug data
      'testing_mode',
      'mock_recordings',
      'icuAppLocalSaves',

      // Deployment and configuration
      'icuAppDeploymentReset'
    ];

    // Remove all known ICU app localStorage keys
    let removedCount = 0;
    icuAppKeys.forEach(key => {
      if (localStorage.getItem(key) !== null) {
        localStorage.removeItem(key);
        removedCount++;
        console.log(`✅ Removed localStorage key: ${key}`);
      }
    });

    // Also scan for any keys starting with 'icuApp' or 'icu_' that might have been missed
    // This catches dynamic keys like receipt mappings (icuAppReceiptMapping_000001, etc.)
    const allKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('icuApp') || key.startsWith('icu_'))) {
        allKeys.push(key);
      }
    }

    // Remove any additional ICU app keys found (including dynamic receipt mappings)
    allKeys.forEach(key => {
      if (!icuAppKeys.includes(key)) {
        localStorage.removeItem(key);
        removedCount++;
        console.log(`✅ Removed additional localStorage key: ${key}`);
      }
    });

    // Clear all sessionStorage (contains temporary session data)
    sessionStorage.clear();
    console.log('✅ Cleared all sessionStorage data');

    console.log(`🎉 Data reset complete! Removed ${removedCount} localStorage keys`);
    console.log('🔒 User privacy ensured - application starting with clean slate');
    console.log('📋 Ready for new user session - all previous data cleared');

  } catch (error) {
    console.error('❌ Error during data reset:', error);
    // Even if there's an error, try to clear what we can
    try {
      localStorage.clear();
      sessionStorage.clear();
      console.log('🔄 Fallback: Cleared all localStorage and sessionStorage');
    } catch (fallbackError) {
      console.error('❌ Fallback data reset also failed:', fallbackError);
    }
  }
};

// Notification component that uses app state
const AppNotifications = () => {
  const { notification, clearNotification } = useAppState();

  return (
    <Snackbar
      open={notification.open}
      autoHideDuration={6000}
      onClose={clearNotification}
    >
      <Alert onClose={clearNotification} severity={notification.severity} sx={{ width: '100%' }}>
        {notification.message}
      </Alert>
    </Snackbar>
  );
};

// Data Reset Wrapper Component
// Ensures complete data reset on every page refresh for user privacy
const DataResetWrapper = ({ children }) => {
  useEffect(() => {
    // Perform complete data reset on component mount (page refresh)
    performCompleteDataReset();
  }, []); // Empty dependency array ensures this runs only once on mount

  return children;
};

// Main App Shell Component - Clean and Focused
const App = () => {
  return (
    <DataResetWrapper>
      <AppStateProvider>
        <SessionStateProvider>
          <RecordingSessionProvider>
            <ThemeProvider theme={createTheme()}>
              <CssBaseline />
              <div className="App">
                <AppContent />
                <AppNotifications />
              </div>
            </ThemeProvider>
          </RecordingSessionProvider>
        </SessionStateProvider>
      </AppStateProvider>
    </DataResetWrapper>
  );
};

export default App;
