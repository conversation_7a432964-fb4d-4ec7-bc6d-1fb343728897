import React from 'react';
import VideoRecorder from './VideoRecorder';
import { Container, Typography, Box } from '@mui/material';

/**
 * Simple test component to isolate and test the lip guide functionality
 */
const LipGuideTest = () => {
  const handleRecordingComplete = (data) => {
    console.log('Test recording complete:', data);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          Lip Guide Test Page
        </Typography>
        <Typography variant="body1" gutterBottom>
          This page is for testing the lip guide overlay functionality.
          The lip guide should appear as a semi-transparent overlay when:
        </Typography>
        <ul>
          <li>Camera permission is granted</li>
          <li>Not currently recording</li>
        </ul>
        
        {/* Test image loading directly */}
        <Box sx={{ mt: 2, mb: 4, p: 2, border: '1px solid #ccc' }}>
          <Typography variant="h6">Direct Image Test:</Typography>
          <img
            src="/images/lips-no-bg.png"
            alt="Direct lip guide test"
            style={{
              width: '150px',
              height: 'auto',
              border: '2px solid blue',
              opacity: 1
            }}
            onLoad={() => console.log('✅ Lip guide image loaded successfully')}
            onError={() => console.error('❌ Lip guide image failed to load')}
          />
        </Box>

        <Box sx={{ mt: 4 }}>
          <VideoRecorder
            onRecordingComplete={handleRecordingComplete}
            disabled={false}
            phrase="Test phrase for lip guide"
            category="Test Category"
            recordingNumber={1}
          />
        </Box>
      </Box>
    </Container>
  );
};

export default LipGuideTest;
