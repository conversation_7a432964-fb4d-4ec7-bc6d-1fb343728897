/**
 * AppContent - Main Application Content Router
 * Handles routing between different application screens while preserving
 * all existing functionality and UI exactly as it was in the original App.js
 */

import React, { useEffect } from 'react';
import { Container, Typography } from '@mui/material';

// Import all existing components (unchanged)
import ConsentPage from './ConsentPage';
import DemographicForm from './DemographicForm';
import TrainingVideoPage from './TrainingVideoPage';
import MouthDetectionTest from './MouthDetectionTest';
import PhraseSelector from './PhraseSelector';
import NavigationMenu from './NavigationMenu';
import S3ProgressPreloader from './S3ProgressPreloader';

// Import the new Recording Session Manager
import RecordingSessionManager from './RecordingSessionManager';
import LipGuideTest from './LipGuideTest';

// Import state providers
import { useAppState } from '../providers/AppStateProvider';
import { useSessionState } from '../providers/SessionStateProvider';
import { useRecordingSession } from '../providers/RecordingSessionProvider';

// Import debug helpers (development only)
import debugHelpers from '../utils/debugHelpers';

// Receipt mapping service temporarily disabled

const AppContent = () => {
  const {
    hasConsent,
    demographicsCompleted,
    trainingVideoCompleted,
    currentStep,
    isMobile,
    setConsent,
    setDemographicsCompleted,
    setTrainingCompleted,
    setCurrentStep,
    showNotification
  } = useAppState();

  const {
    demographicInfo,
    setDemographicInfo
  } = useSessionState();

  const {
    phrasesSelected,
    setSelectedPhrases
  } = useRecordingSession();

  // Initialize debug helpers and receipt mapping service
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Debug helpers will be initialized with context references
      // This is handled differently to avoid hook call issues
      console.log('🔧 Development mode: Debug helpers available');
    }

    // Initialize receipt mapping service - temporarily disabled
    const initializeReceiptMapping = async () => {
      try {
        console.log('🧾 Receipt Mapping Service temporarily disabled');
        // Receipt mapping functionality temporarily disabled during restoration
      } catch (error) {
        console.error('❌ Error initializing Receipt Mapping Service:', error);
      }
    };

    initializeReceiptMapping();
  }, [showNotification]);

  // Handle consent submission (preserved from original App.js)
  const handleConsent = () => {
    setConsent(true);
    setCurrentStep('demographics');
    showNotification(
      'Thank you for your consent. Please complete the demographics form.',
      'success'
    );
  };

  // Handle demographic form submission (preserved from original App.js)
  const handleDemographicSubmit = (demographicData) => {
    setDemographicInfo(demographicData);
    setDemographicsCompleted(true);
    setCurrentStep('training');

    showNotification(
      'Demographics saved successfully. Please watch the training video.',
      'success'
    );
  };

  // Handle back to consent (preserved from original App.js)
  const handleBackToConsent = () => {
    setConsent(false);
    setCurrentStep('demographics'); // Changed from 'home' since we removed home
  };

  // Handle training video completion (preserved from original App.js)
  const handleTrainingVideoComplete = () => {
    setTrainingCompleted(true);
    setCurrentStep('phrases');
    showNotification(
      'Training completed. Please select phrases to record.',
      'success'
    );
  };

  // Handle navigation (preserved from original App.js)
  const handleNavigation = (step) => {
    console.log('handleNavigation called with step:', step);
    setCurrentStep(step);
  };

  return (
    <div>
      {/* S3 Progress Preloader - starts loading data in background (preserved) */}
      <S3ProgressPreloader preloadOnMount={true} preloadDelay={3000} />

      {/* Navigation Menu - only show when user has consent (preserved) */}
      {hasConsent && (
        <NavigationMenu
          onNavigate={handleNavigation}
          currentStep={currentStep}
          hasConsent={hasConsent}
          demographicsCompleted={demographicsCompleted}
          phrasesSelected={phrasesSelected}
        />
      )}

      {/* Main Content Container (preserved styling) */}
      <Container maxWidth="lg" disableGutters={isMobile} sx={{ px: isMobile ? 1 : 2 }}>
        {/* Consent Flow - exactly as it was */}
        {!hasConsent ? (
          <ConsentPage onConsent={handleConsent} />
        ) : !demographicsCompleted ? (
          <DemographicForm
            onSubmit={handleDemographicSubmit}
            onBackToConsent={handleBackToConsent}
            currentDemographics={demographicInfo}
          />
        ) : currentStep === 'training' ? (
          <TrainingVideoPage onComplete={handleTrainingVideoComplete} />
        ) : currentStep === 'mouth-test' ? (
          <MouthDetectionTest />
        ) : currentStep === 'phrases' ? (
          <PhraseSelector onPhrasesSelected={(phrases) => {
            // Save phrases to RecordingSessionProvider and navigate to recording
            setSelectedPhrases(phrases);
            setCurrentStep('recording');
            showNotification(
              `${phrases.length} phrases selected for recording.`,
              'success'
            );
          }} />
        ) : currentStep === 'recording' ? (
          <RecordingSessionManager />
        ) : currentStep === 'lip-guide-test' ? (
          <LipGuideTest />
        ) : (
          <Typography>Unknown step</Typography>
        )}
      </Container>
    </div>
  );
};

export default AppContent;
