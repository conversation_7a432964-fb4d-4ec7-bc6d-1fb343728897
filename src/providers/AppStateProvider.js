/**
 * AppStateProvider - Global Application State Management
 * Handles high-level application state including authentication flow,
 * navigation, and global UI state
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Action types
const APP_ACTIONS = {
  SET_CONSENT: 'SET_CONSENT',
  SET_DEMOGRAPHICS_COMPLETED: 'SET_DEMOGRAPHICS_COMPLETED',
  SET_TRAINING_COMPLETED: 'SET_TRAINING_COMPLETED',
  SET_CURRENT_STEP: 'SET_CURRENT_STEP',
  SET_NOTIFICATION: 'SET_NOTIFICATION',
  CLEAR_NOTIFICATION: 'CLEAR_NOTIFICATION',
  SET_UPLOADING: 'SET_UPLOADING'
};

// Initial state
const initialState = {
  // Authentication flow
  hasConsent: false,
  demographicsCompleted: false,
  trainingVideoCompleted: false,
  
  // Navigation
  currentStep: 'demographics', // Start with demographics instead of home
  
  // Global UI state
  notification: { open: false, message: '', severity: 'info' },
  uploading: false,
  
  // Device detection
  isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
  isLaptop: window.innerWidth > 768
};

// Reducer
const appStateReducer = (state, action) => {
  switch (action.type) {
    case APP_ACTIONS.SET_CONSENT:
      return {
        ...state,
        hasConsent: action.payload,
        currentStep: action.payload ? 'demographics' : 'consent'
      };
      
    case APP_ACTIONS.SET_DEMOGRAPHICS_COMPLETED:
      return {
        ...state,
        demographicsCompleted: action.payload,
        currentStep: action.payload ? 'training' : 'demographics'
      };
      
    case APP_ACTIONS.SET_TRAINING_COMPLETED:
      return {
        ...state,
        trainingVideoCompleted: action.payload,
        currentStep: action.payload ? 'phrases' : 'training'
      };
      
    case APP_ACTIONS.SET_CURRENT_STEP:
      return {
        ...state,
        currentStep: action.payload
      };
      
    case APP_ACTIONS.SET_NOTIFICATION:
      return {
        ...state,
        notification: {
          open: true,
          message: action.payload.message,
          severity: action.payload.severity || 'info'
        }
      };
      
    case APP_ACTIONS.CLEAR_NOTIFICATION:
      return {
        ...state,
        notification: { ...state.notification, open: false }
      };
      
    case APP_ACTIONS.SET_UPLOADING:
      return {
        ...state,
        uploading: action.payload
      };
      
    default:
      return state;
  }
};

// Context
const AppStateContext = createContext();

// Provider component
export const AppStateProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appStateReducer, initialState);

  // Action creators
  const actions = {
    setConsent: (hasConsent) => {
      dispatch({ type: APP_ACTIONS.SET_CONSENT, payload: hasConsent });
    },
    
    setDemographicsCompleted: (completed) => {
      dispatch({ type: APP_ACTIONS.SET_DEMOGRAPHICS_COMPLETED, payload: completed });
    },
    
    setTrainingCompleted: (completed) => {
      dispatch({ type: APP_ACTIONS.SET_TRAINING_COMPLETED, payload: completed });
    },
    
    setCurrentStep: (step) => {
      dispatch({ type: APP_ACTIONS.SET_CURRENT_STEP, payload: step });
    },
    
    showNotification: (message, severity = 'info') => {
      dispatch({ 
        type: APP_ACTIONS.SET_NOTIFICATION, 
        payload: { message, severity } 
      });
    },
    
    clearNotification: () => {
      dispatch({ type: APP_ACTIONS.CLEAR_NOTIFICATION });
    },
    
    setUploading: (uploading) => {
      dispatch({ type: APP_ACTIONS.SET_UPLOADING, payload: uploading });
    }
  };

  // Handle URL parameters for direct access
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const directAccess = urlParams.get('direct');
    
    if (directAccess === 'recording') {
      console.log('Direct access to recording page detected');
      actions.setConsent(true);
      actions.setDemographicsCompleted(true);
      actions.setTrainingCompleted(true);
      actions.setCurrentStep('recording');
    } else if (directAccess === 'training') {
      console.log('Direct access to training page detected');
      actions.setConsent(true);
      actions.setDemographicsCompleted(true);
      actions.setCurrentStep('training');
    } else if (directAccess === 'mouth-test') {
      console.log('Direct access to mouth detection test detected');
      actions.setCurrentStep('mouth-test');
    } else if (directAccess === 'lip-guide-test') {
      console.log('Direct access to lip guide test detected');
      actions.setCurrentStep('lip-guide-test');
    }
  }, []);

  const value = {
    ...state,
    ...actions
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
};

// Hook to use app state
export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

export default AppStateProvider;
